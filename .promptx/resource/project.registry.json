{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-29T19:33:19.386Z", "updatedAt": "2025-07-29T19:33:19.394Z", "resourceCount": 17}, "resources": [{"id": "dialogue-management", "source": "project", "protocol": "execution", "name": "Dialogue Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/dialogue-management.execution.md", "metadata": {"createdAt": "2025-07-29T19:33:19.388Z", "updatedAt": "2025-07-29T19:33:19.388Z", "scannedAt": "2025-07-29T19:33:19.388Z", "path": "role/fury/execution/dialogue-management.execution.md"}}, {"id": "fury-workflow", "source": "project", "protocol": "execution", "name": "Fury Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/fury-workflow.execution.md", "metadata": {"createdAt": "2025-07-29T19:33:19.389Z", "updatedAt": "2025-07-29T19:33:19.389Z", "scannedAt": "2025-07-29T19:33:19.389Z", "path": "role/fury/execution/fury-workflow.execution.md"}}, {"id": "resume-generation", "source": "project", "protocol": "execution", "name": "Resume Generation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/resume-generation.execution.md", "metadata": {"createdAt": "2025-07-29T19:33:19.389Z", "updatedAt": "2025-07-29T19:33:19.389Z", "scannedAt": "2025-07-29T19:33:19.389Z", "path": "role/fury/execution/resume-generation.execution.md"}}, {"id": "fury", "source": "project", "protocol": "role", "name": "Fury 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fury/fury.role.md", "metadata": {"createdAt": "2025-07-29T19:33:19.389Z", "updatedAt": "2025-07-29T19:33:19.389Z", "scannedAt": "2025-07-29T19:33:19.389Z", "path": "role/fury/fury.role.md"}}, {"id": "agent-broker-mindset", "source": "project", "protocol": "thought", "name": "Agent Broker Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/agent-broker-mindset.thought.md", "metadata": {"createdAt": "2025-07-29T19:33:19.390Z", "updatedAt": "2025-07-29T19:33:19.390Z", "scannedAt": "2025-07-29T19:33:19.390Z", "path": "role/fury/thought/agent-broker-mindset.thought.md"}}, {"id": "value-discovery-techniques", "source": "project", "protocol": "thought", "name": "Value Discovery Techniques 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/value-discovery-techniques.thought.md", "metadata": {"createdAt": "2025-07-29T19:33:19.390Z", "updatedAt": "2025-07-29T19:33:19.390Z", "scannedAt": "2025-07-29T19:33:19.390Z", "path": "role/fury/thought/value-discovery-techniques.thought.md"}}, {"id": "core-management", "source": "project", "protocol": "execution", "name": "Core Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/core-management.execution.md", "metadata": {"createdAt": "2025-07-29T19:33:19.391Z", "updatedAt": "2025-07-29T19:33:19.391Z", "scannedAt": "2025-07-29T19:33:19.391Z", "path": "role/pepper/execution/core-management.execution.md"}}, {"id": "research-planning", "source": "project", "protocol": "execution", "name": "Research Planning 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/research-planning.execution.md", "metadata": {"createdAt": "2025-07-29T19:33:19.391Z", "updatedAt": "2025-07-29T19:33:19.391Z", "scannedAt": "2025-07-29T19:33:19.391Z", "path": "role/pepper/execution/research-planning.execution.md"}}, {"id": "tool-orchestration", "source": "project", "protocol": "execution", "name": "Tool Orchestration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/tool-orchestration.execution.md", "metadata": {"createdAt": "2025-07-29T19:33:19.391Z", "updatedAt": "2025-07-29T19:33:19.391Z", "scannedAt": "2025-07-29T19:33:19.391Z", "path": "role/pepper/execution/tool-orchestration.execution.md"}}, {"id": "pepper", "source": "project", "protocol": "role", "name": "Pepper 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pepper/pepper.role.md", "metadata": {"createdAt": "2025-07-29T19:33:19.391Z", "updatedAt": "2025-07-29T19:33:19.391Z", "scannedAt": "2025-07-29T19:33:19.391Z", "path": "role/pepper/pepper.role.md"}}, {"id": "adaptive-learning", "source": "project", "protocol": "thought", "name": "Adaptive Learning 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/adaptive-learning.thought.md", "metadata": {"createdAt": "2025-07-29T19:33:19.392Z", "updatedAt": "2025-07-29T19:33:19.392Z", "scannedAt": "2025-07-29T19:33:19.392Z", "path": "role/pepper/thought/adaptive-learning.thought.md"}}, {"id": "verification-mindset", "source": "project", "protocol": "thought", "name": "Verification Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/verification-mindset.thought.md", "metadata": {"createdAt": "2025-07-29T19:33:19.392Z", "updatedAt": "2025-07-29T19:33:19.392Z", "scannedAt": "2025-07-29T19:33:19.392Z", "path": "role/pepper/thought/verification-mindset.thought.md"}}, {"id": "vision-document-management", "source": "project", "protocol": "execution", "name": "Vision Document Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vision/execution/vision-document-management.execution.md", "metadata": {"createdAt": "2025-07-29T19:33:19.393Z", "updatedAt": "2025-07-29T19:33:19.393Z", "scannedAt": "2025-07-29T19:33:19.393Z", "path": "role/vision/execution/vision-document-management.execution.md"}}, {"id": "vision-enhanced-task-workflow", "source": "project", "protocol": "execution", "name": "Vision Enhanced Task Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vision/execution/vision-enhanced-task-workflow.execution.md", "metadata": {"createdAt": "2025-07-29T19:33:19.393Z", "updatedAt": "2025-07-29T19:33:19.393Z", "scannedAt": "2025-07-29T19:33:19.393Z", "path": "role/vision/execution/vision-enhanced-task-workflow.execution.md"}}, {"id": "vision-analytical-mind", "source": "project", "protocol": "thought", "name": "Vision Analytical Mind 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vision/thought/vision-analytical-mind.thought.md", "metadata": {"createdAt": "2025-07-29T19:33:19.394Z", "updatedAt": "2025-07-29T19:33:19.394Z", "scannedAt": "2025-07-29T19:33:19.394Z", "path": "role/vision/thought/vision-analytical-mind.thought.md"}}, {"id": "vision-task-strategy", "source": "project", "protocol": "thought", "name": "Vision Task Strategy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vision/thought/vision-task-strategy.thought.md", "metadata": {"createdAt": "2025-07-29T19:33:19.394Z", "updatedAt": "2025-07-29T19:33:19.394Z", "scannedAt": "2025-07-29T19:33:19.394Z", "path": "role/vision/thought/vision-task-strategy.thought.md"}}, {"id": "vision", "source": "project", "protocol": "role", "name": "Vision 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/vision/vision.role.md", "metadata": {"createdAt": "2025-07-29T19:33:19.394Z", "updatedAt": "2025-07-29T19:33:19.394Z", "scannedAt": "2025-07-29T19:33:19.394Z", "path": "role/vision/vision.role.md"}}], "stats": {"totalResources": 17, "byProtocol": {"execution": 8, "role": 3, "thought": 6}, "bySource": {"project": 17}}}